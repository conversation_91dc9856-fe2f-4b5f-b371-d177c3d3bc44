package com.yancao.qrscanner.camera

import android.content.Context
import androidx.camera.core.AspectRatio
import androidx.camera.core.CameraSelector
import androidx.camera.core.ExperimentalGetImage
import androidx.camera.core.ImageAnalysis
import androidx.camera.core.Preview
import androidx.camera.core.UseCase
import androidx.camera.core.resolutionselector.AspectRatioStrategy
import androidx.camera.core.resolutionselector.ResolutionSelector
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.view.PreviewView
import androidx.core.content.ContextCompat
import androidx.lifecycle.LifecycleOwner
import com.google.mlkit.vision.barcode.common.Barcode
import java.util.concurrent.Executors

@ExperimentalGetImage
class CameraManager(private val context: Context) {

    private var cameraProvider: ProcessCameraProvider? = null
    private val cameraExecutor = Executors.newSingleThreadExecutor()

    // 新增：图像分析器用于实时二维码扫描
    private var imageAnalyzer: ImageAnalysis? = null
    private var qrAnalyzer: RealtimeQRAnalyzer? = null

    @ExperimentalGetImage
    fun startCamera(
        previewView: PreviewView,
        lifecycleOwner: LifecycleOwner,
        enableRealtimeScanning: Boolean = false,
        onQRCodeDetected: ((List<String>) -> Unit)? = null,
        // 新增：二维码位置信息回调
        onQRCodeWithPosition: ((List<Barcode>, Int, Int) -> Unit)? = null
    ) {
        val cameraProviderFuture = ProcessCameraProvider.getInstance(context)

        cameraProviderFuture.addListener({
            cameraProvider = cameraProviderFuture.get()

            val resolutionSelector = ResolutionSelector.Builder()
                .setAspectRatioStrategy(AspectRatioStrategy.RATIO_16_9_FALLBACK_AUTO_STRATEGY)
                .build()

            // Preview配置
            val preview = Preview.Builder()
                .setResolutionSelector(resolutionSelector)
                .build()
                .also {
                    it.setSurfaceProvider(previewView.surfaceProvider)
                }

            // 新增：如果启用实时扫描，创建图像分析器
            val useCases = mutableListOf<UseCase>(preview)

            if (enableRealtimeScanning) {
                qrAnalyzer = RealtimeQRAnalyzer(onQRCodeDetected, onQRCodeWithPosition)
                imageAnalyzer = ImageAnalysis.Builder()
                    .setBackpressureStrategy(ImageAnalysis.STRATEGY_KEEP_ONLY_LATEST)
                    .setResolutionSelector(resolutionSelector) // 使用相同的分辨率选择器
                    .build()
                    .also {
                        it.setAnalyzer(cameraExecutor, qrAnalyzer!!)
                    }
                useCases.add(imageAnalyzer!!)
            }

            try {
                cameraProvider?.unbindAll()
                cameraProvider?.bindToLifecycle(
                    lifecycleOwner,
                    CameraSelector.DEFAULT_BACK_CAMERA,
                    *useCases.toTypedArray() // 绑定所有用例
                )
            } catch (e: Exception) {
                e.printStackTrace()
            }

        }, ContextCompat.getMainExecutor(context))
    }


    fun shutdown() {
        cameraExecutor.shutdown()
    }
}